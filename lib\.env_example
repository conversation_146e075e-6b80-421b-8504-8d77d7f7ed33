################################################################################
# Environment Configuration File for LuminariMUD
################################################################################
#
# This is an example configuration file for LuminariMUD environment variables.
#
# CRITICAL: FILE LOCATION REQUIREMENT
# ===================================
# The '.env' file MUST be placed in the lib/ directory!
# The MUD looks for this file at: lib/.env
#
# To use this file:
#   1. Copy this file to 'lib/.env' (without the .example suffix)
#      Example: cp .env.example lib/.env
#   2. Edit the values below with your actual configuration
#   3. Ensure the .env file has appropriate permissions (chmod 600)
#      Example: chmod 600 lib/.env
#
# IMPORTANT: The actual '.env' file is git-ignored to protect your
#            API keys and credentials. Never commit real credentials to version control!
#
################################################################################

# Application Environment
# Options: development, testing, production
APP_ENV=production

# Application Timezone
APP_TIMEZONE=UTC

# Database Configuration
DB_HOST=localhost
DB_PORT=3306
DB_NAME=luminarimud
DB_USER=your_username
DB_PASS=your_password

# Security Configuration
# Generate a random 32-character string for this
APP_SECRET=your_32_character_secret_key_here

# Session Configuration
SESSION_LIFETIME=3600

# Cache Configuration
CACHE_ENABLED=true
CACHE_TTL=3600

# Logging Configuration
LOG_LEVEL=error
LOG_FILE=logs/application.log

# Authentication Configuration
# Set to true to require authentication for all tools
REQUIRE_AUTH=false

# Admin Configuration
# Default admin credentials (change these!)
ADMIN_USERNAME=admin
ADMIN_PASSWORD=change_this_password

# Rate Limiting
RATE_LIMIT_ENABLED=true
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=3600

# File Upload Configuration
MAX_UPLOAD_SIZE=10M
ALLOWED_EXTENSIONS=txt,csv,json

# Email Configuration (for notifications)
MAIL_HOST=localhost
MAIL_PORT=587
MAIL_USERNAME=
MAIL_PASSWORD=
MAIL_FROM=<EMAIL>

# Backup Configuration
BACKUP_ENABLED=false
BACKUP_PATH=backups/
BACKUP_RETENTION_DAYS=30

################################################################################
# AI Service Configuration
################################################################################
#
# OpenAI API key for AI-powered NPC dialogue
# - Get your API key from: https://platform.openai.com/api-keys
# - Format: sk-proj-XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX
# - Keep this secret! Anyone with this key can use your OpenAI account
# - Example: OPENAI_API_KEY=sk-proj-abc123def456...
OPENAI_API_KEY=

# AI Model Selection
# - Available models: gpt-4.1-mini, gpt-4o-mini, gpt-4.1, gpt-4o
# - gpt-4.1-mini is fastest and most cost-effective (83% cheaper than gpt-4o)
# - gpt-4o-mini is also very cost-effective ($0.15/1M input, $0.60/1M output tokens)
# - gpt-4.1 provides best quality with 1M token context window
AI_MODEL=gpt-4.1-mini

# Maximum Response Length (in tokens)
# - 1 token ≈ 4 characters in English
# - Higher values = longer responses but higher costs
# - Recommended: 100-500 for NPC dialogue
AI_MAX_TOKENS=500

# Response Creativity (0.0-1.0)
# - 0.0 = Very focused and deterministic
# - 0.7 = Balanced creativity (recommended)
# - 1.0 = Very creative but may be unpredictable
AI_TEMPERATURE=0.7

# Rate Limiting
# - Prevents excessive API usage and cost overruns
# - Adjust based on your OpenAI plan limits
AI_REQUESTS_PER_MINUTE=60
AI_REQUESTS_PER_HOUR=1000

# Cache Settings
# - How long to cache AI responses (in seconds)
# - Higher values reduce API calls but responses may become repetitive
# - 3600 seconds = 1 hour
AI_CACHE_EXPIRE_SECONDS=3600

# Content Filtering
# - Enable to filter inappropriate content
# - Recommended for public servers
AI_CONTENT_FILTER_ENABLED=true
