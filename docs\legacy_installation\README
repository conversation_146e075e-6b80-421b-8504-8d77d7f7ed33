Updated: Apr 2007
                                tbaMUD README
				-------------
All requests for help or bugs should be reported to: tbamud.com 9091.

Information about CircleMUD can be found at the CircleMUD Home Page and FTP:
http://www.circlemud.org

Although outdated, the CircleMUD archives contain many great ideas in the
/contrib/ section.

Both of these sites are outdated and no longer maintained. The latest 
information and downloads can be found at:
http://tbamud.com

Downloading tbaMUD
---------------------

You can find the current version of tbaMUD at the following URL:
http://tbamud.com

Compiling
-------------------

tbaMU<PERSON> compiles under a large number of operating systems; instructions
for compiling on each platform is can be found in the file doc/README.<system>
Many of these instructions are outdated, please feel free to update them.

AMIGA   - If you are using an Amiga running AmigaDOS. (If you're running 
          NetBSD or Linux on an Amiga, use README.UNIX instead.)
ARC     - If you are using an Acorn running RiscOS.
BORLAND - For those using Borland under Windows.
CYGWIN  - If you are using Cygwin shell in Windows.
MSVC#   - If you are using MSVC.
OS2     - If you are using OS/2 Warp Connect v3.0 or OS/2 v2.x.
UNIX    - If you have any type UNIX system, including Linux, MkLinux, Ultrix, 
          HP/UX, Solaris, SunOS, IRIX, FreeBSD, OpenBSD, NetBSD, BSDi, 
          Macintosh OS X, etc.
VMS     - If you happen to be on OpenVMS.
WATCOM  - If using WATCOM under Windows.
WIN     - If you are using Windows.

If you are interested in porting tbaMUD to a new platform, see the file 
doc/porting.txt for some tips.

For a small, private MUD, or a MUD used only for testing and development, 
about 10 megs of disk space and 16 megs of memory should be sufficient.
For large, public MUDs with a large player base, 30 megs to 50 megs of
disk space and at least 32 megs of memory are recommended.  Free memory
is much more important than CPU speed; CircleMUD uses virtually no CPU
time.


Other Documentation
-------------------

If this information isn't enough to get you running, there's a lot more
information available.  All documentation (other than this file) is in
the "doc" directory. Most of the files in the /doc directory are old and
have not been updated in years, but they still contain a lot of pertinent
information.

"The CircleMUD Administrator's Guide" (admin.pdf)
    A good place to start after reading this README file, admin.txt gives
an overall description of how Circle works, how to get it to compile and
run for the first time, information about customizing and configuration
options and command-line arguments, and tips on maintenance and day-to-day
MUD administration. 

"The CircleMUD Builder's Manual" (building.pdf)
    For the builders in your group, this documents the world-file format 
and describes how to create new rooms, objects, and monsters.  Also, it 
describes how to add new areas to the MUD and gives some tips about game 
balance and world-file debugging.

"The CircleMUD Coder's Manual" (coding.pdf)
    For the coders in your group, a technical reference describing some of
the more basic coding tasks such as how to add new commands, spells,
skills, socials, and classes.  Note that it assumes the reader already has
an excellent knowledge of C; the manual is not a C tutorial.

"The CircleMUD SYSERR List" (syserr.txt)
    A list of SYSERR messages and a description of what can cause each problem
and how to solve it.  An excellent guide for troubleshooting and area debugging.


Getting Help
------------

If you have strange problems -- and you can't figure out the answer by reading 
the documentation -- fear not, there are many other resources available.

http://cwg.lazuras.org/
http://tbamud.com/

No matter how you choose to get help, make sure to always include the
following information:

  -- The exact version you are using (i.e., "tbaMUD 3.51", etc.).
  -- The EXACT text of any error messages, compiler errors, link errors, or any
     other errors you're getting.
  -- The exact type of hardware, operating system name and version, and
     compiler you're using.
  -- A description of ANY changes you've made, no matter how small, that might
     have contributed to the error.
  -- If you are having trouble getting Circle running for the very first time, 
     also be sure to include the output of 'configure' and the file
     'config.log'.

You may also stop by the Builder Academy at anytime. tbamud.com 9091

--Rumble
