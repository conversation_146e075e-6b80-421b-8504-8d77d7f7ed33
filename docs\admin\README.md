# Luminari MUD Administrator's Guide
# Assembled by <PERSON><PERSON><PERSON>
# Last Updated: July 27, 2025

## Table of Contents

1. [Introduction](#introduction)
2. [Getting Started](#getting-started)
3. [Installation and Configuration](#installation-and-configuration)
4. [Running the Server](#running-the-server)
5. [System Administration](#system-administration)
6. [Maintenance and Monitoring](#maintenance-and-monitoring)
7. [Troubleshooting](#troubleshooting)
8. [Frequently Asked Questions](#frequently-asked-questions)

## Introduction

### About Luminari MUD

Luminari MUD is a derivative of tbaMUD, which itself is derived from CircleMUD and ultimately DikuMUD. This codebase represents thousands of hours of development work, creating a stable, extensible foundation for Multi-User Dungeon (MUD) games.

**Key Features:**
- Highly optimized and stable codebase
- Extensive online creation (OLC) system
- Advanced scripting capabilities
- Comprehensive administrative tools
- Cross-platform compatibility

### Philosophy and Vision

Luminari MUD is designed as a launching pad for your own MUD ideas. Rather than providing a complete game with dozens of classes and hundreds of spells, it provides a solid foundation that encourages creativity and customization. The goal is to free MUD implementors from dealing with bug-ridden code or reinventing basic MUD features, allowing them to focus on creative development.

### Are You Ready to Run a MUD?

**Important Considerations:**
- Running a MUD is extremely time-consuming if done well
- You'll need to deal with player management and community issues
- Technical maintenance requires ongoing attention
- Consider gaining experience as a player and administrator on other MUDs first

**Recommended Prerequisites:**
- Experience playing MUDs
- Basic understanding of Unix/Linux systems
- Familiarity with C programming (for customization)
- Time commitment for ongoing maintenance

## Getting Started

### System Requirements

**Minimum Requirements:**
- 20 MB disk space (small/private MUD)
- 32 MB RAM (small/private MUD)
- Unix-like operating system or Windows with Cygwin

**Recommended for Production:**
- 30-50 MB disk space
- 50+ MB RAM
- Dedicated server or VPS
- Regular backup system

**Supported Platforms:**
- Linux (all major distributions)
- Unix variants (Solaris, AIX, HP-UX, etc.)
- macOS (10.0 and above)
- Windows (with Cygwin or WSL)
- BSD variants
- Other POSIX-compliant systems

### Hardware Considerations

**CPU:** MUDs use very little CPU time. A modest processor is sufficient.
**Memory:** Free memory is much more important than CPU speed.
**Storage:** Fast disk I/O helps with player file operations and world saves.
**Network:** Stable internet connection with adequate bandwidth for player count.

## Installation and Configuration

### Downloading and Unpacking

1. **Download the latest version** from the official repository
2. **Extract the archive:**
   ```bash
   tar -xzf luminari-source.tar.gz
   cd Luminari-Source
   ```

### Configuration Process

**For Unix/Linux Systems:**
```bash
./configure
```

**Configuration Options:**
- The configure script automatically detects system characteristics
- Creates appropriate Makefiles and conf.h header file
- Only needs to be run once (or when moving to different hardware)
- Delete config.cache if reconfiguring

**For Windows Users:**
- See platform-specific installation guides
- Consider using WSL (Windows Subsystem for Linux)
- Cygwin is also supported

### Compilation

**Basic Compilation:**
```bash
cd src
make
```

**Compilation Options:**
- `make` - Compile the main MUD server
- `make utils` - Compile utility programs
- `make all` - Compile both server and utilities
- `make clean` - Clean compiled objects

**Common Compilation Issues:**
- Ensure all dependencies are installed
- Check that configure completed successfully
- Verify compiler compatibility
- Review error messages for missing libraries

## Running the Server

### Initial Setup

**1. Create Directory Structure:**
The MUD expects specific directories:
- `lib/` - Game data and configuration
- `log/` - System logs
- `bin/` - Compiled binaries

**2. Initialize World Files:**
- Copy world files to `lib/world/`
- Set up initial configuration files
- Create administrator character

**3. First Run:**
```bash
cd bin
./circle
```

### Autorun Script

**Using the Autorun Script:**
```bash
./autorun &
```

**Autorun Features:**
- Automatic restart on crashes
- Log rotation
- Memory usage monitoring
- Customizable restart conditions

**Autorun Configuration:**
Edit the autorun script to customize:
- Port numbers
- Log file locations
- Restart conditions
- Email notifications

### Command Line Options

**Common Options:**
- `-p <port>` - Specify port number
- `-d <directory>` - Set lib directory
- `-l` - Enable system logging
- `-s` - Suppress some boot messages
- `-c` - Enable syntax checking mode

### Creating Your First Administrator

**In-Game Creation:**
1. Connect to your MUD
2. Create a character
3. Use the advance command to set level 34 (implementor)
4. Set appropriate privileges

**Direct Database Modification:**
- Edit player files directly (advanced users only)
- Use utility programs for player management
��
 
 
## System Administration

### File Structure Overview

**Main Directories:**
```
Luminari-Source/
├── bin/           # Compiled binaries
├── lib/           # Game data
│   ├── etc/       # System files (maintained by game)
│   ├── house/     # Player house files
│   ├── misc/      # Database files
│   ├── plrfiles/  # Player data files
│   ├── plrobjs/   # Player object files
│   ├── text/      # Help files, MOTD, etc.
│   └── world/     # World files (rooms, mobs, objects)
├── log/           # System logs
├── src/           # Source code
└── docs/ # Documentation files
```

**Critical Files:**
- `lib/etc/config` - Main configuration file
- `lib/text/motd` - Message of the day
- `lib/text/imotd` - Immortal MOTD
- `lib/text/help/*` - Help files
- `lib/misc/socials` - Social commands

### Configuration Management

**CEDIT - Configuration Editor:**
- Access via `cedit` command in-game
- Modify server settings without restart
- Save changes with `cedit save`

**Key Configuration Options:**
- Player limits and restrictions
- Economic settings
- Combat parameters
- Experience and leveling
- Channel settings

**Text File Management:**
**TEDIT - Text Editor:**
- Edit help files, MOTD, and other text files
- Use `tedit <filename>` command
- Changes take effect immediately with `reload` command

### Player Management

**Player File System:**
- ASCII-based player files (modern format)
- Located in `lib/plrfiles/`
- Organized by first letter of name

**Common Administrative Commands:**
- `advance <player> <level>` - Change player level
- `set <player> <field> <value>` - Modify player attributes
- `purge <player>` - Remove player from game
- `freeze <player>` - Temporarily disable account
- `ban <site>` - Ban IP addresses or hostnames

**Player Statistics:**
- `users` - Show current players
- `stat <player>` - Display player information
- `last <player>` - Show login history
- `uptime` - Server statistics

### Security and Access Control

**Site Banning:**
- `ban <site>` - Ban by hostname or IP
- `unban <site>` - Remove ban
- Edit `lib/etc/badsites` file directly

**Player Restrictions:**
- `freeze <player>` - Disable account temporarily
- `thaw <player>` - Re-enable frozen account
- `notitle <player>` - Prevent custom titles
- `mute <player>` - Disable communication

**Immortal Levels:**
- Level 31: Lesser God (basic immortal commands)
- Level 32: God (advanced commands)
- Level 33: Greater God (world modification)
- Level 34: Implementor (full access)

## Maintenance and Monitoring

### System Logs

**Log Files Location:** `log/` directory

**Key Log Files:**
- `syslog` - Main system log
- `syslog.CRASH` - Crash information
- `usage` - Player usage statistics
- `newplayers` - New player registrations
- `levels` - Level advancement log
- `godcmds` - Immortal command usage
- `badpws` - Failed login attempts

**Log Monitoring:**
- Use `file` command in-game to view logs
- Regular log rotation prevents disk space issues
- Monitor for unusual patterns or errors

### Performance Monitoring

**Memory Usage:**
- Monitor with system tools (top, htop, ps)
- Watch for memory leaks
- Regular restarts if memory grows excessively

**Player Load:**
- Track concurrent users
- Monitor lag and response times
- Adjust limits based on server capacity

**Database Maintenance:**
- Regular player file cleanup
- Remove inactive accounts
- Backup critical data regularly

### Backup Procedures

**Critical Files to Backup:**
- `lib/plrfiles/` - Player data
- `lib/plrobjs/` - Player objects
- `lib/etc/` - Configuration files
- `lib/world/` - World files (if modified)
- `lib/text/` - Help files and MOTD

**Backup Schedule:**
- Daily: Player files and objects
- Weekly: Full system backup
- Before updates: Complete backup

**Backup Commands:**
```bash
# Create backup directory
mkdir -p backups/$(date +%Y%m%d)

# Backup player data
cp -r lib/plrfiles/ backups/$(date +%Y%m%d)/
cp -r lib/plrobjs/ backups/$(date +%Y%m%d)/

# Backup configuration
cp -r lib/etc/ backups/$(date +%Y%m%d)/
```

### Regular Maintenance Tasks

**Daily:**
- Check system logs for errors
- Monitor player activity
- Verify server stability
- Check disk space usage

**Weekly:**
- Full system backup
- Review and clean old log files
- Update help files if needed
- Check for code updates

**Monthly:**
- Purge inactive player accounts
- Review and update policies
- Performance analysis
- Security audit
## Troubleshooting

### Common Issues

**Server Won't Start:**
1. Check port availability: `netstat -an | grep <port>`
2. Verify file permissions
3. Check configuration file syntax
4. Review system logs for errors

**Frequent Crashes:**
1. Check system logs and crash dumps
2. Monitor memory usage
3. Review recent code changes
4. Check for corrupted data files

**Performance Issues:**
1. Monitor system resources
2. Check for infinite loops in scripts
3. Review database queries
4. Analyze player load patterns

**Player File Corruption:**
1. Restore from backup
2. Use player file utilities
3. Check disk space and permissions
4. Verify file system integrity

### Debugging Tools

**Built-in Commands:**
- `stat` - Object and character information
- `show` - Display various game statistics
- `syslog` - View system messages
- `wizlock` - Restrict access during maintenance

**External Tools:**
- `gdb` - GNU debugger for crash analysis
- `valgrind` - Memory leak detection
- `strace` - System call tracing
- Log analysis scripts

### Emergency Procedures

**Server Crash Recovery:**
1. Check for core dump files
2. Restart server with autorun script
3. Verify player file integrity
4. Notify players of any data loss

**Data Corruption:**
1. Stop server immediately
2. Restore from most recent backup
3. Investigate cause of corruption
4. Implement preventive measures

**Security Breach:**
1. Change all administrative passwords
2. Review access logs
3. Check for unauthorized modifications
4. Implement additional security measures

## Frequently Asked Questions

### General Questions

**Q: How many players can my MUD support?**
A: This depends on your hardware and network connection. A typical server can handle 50-100 concurrent players comfortably. Monitor memory usage and response times to determine your limits.

**Q: How often should I restart the server?**
A: Modern MUD servers can run for weeks or months without restart. Restart only when necessary for updates or if you notice memory leaks.

**Q: Can I modify the game while it's running?**
A: Yes! The OLC (Online Creation) system allows real-time world modification. Code changes require compilation and restart.

### Technical Questions

**Q: Why won't my MUD compile?**
A: Common issues include:
- Missing development libraries
- Incorrect compiler version
- Configuration not run properly
- Platform-specific issues

**Q: How do I add new areas to my MUD?**
A: Use the OLC system or create world files manually. Add zone entries to the zone table and ensure proper numbering.

**Q: Can I run multiple MUDs on one server?**
A: Yes, use different ports and lib directories for each instance. Ensure adequate resources for all instances.

### Administrative Questions

**Q: How do I handle problem players?**
A: Use administrative commands like `freeze`, `mute`, or `ban`. Document incidents and maintain consistent policies.

**Q: What's the best way to backup player data?**
A: Implement automated daily backups of the `lib/plrfiles/` and `lib/plrobjs/` directories. Test restore procedures regularly.

**Q: How do I update help files?**
A: Use the `tedit` command in-game or edit files in `lib/text/help/` directly. Use `reload` to apply changes.

### Performance Questions

**Q: My MUD is running slowly. What should I check?**
A: Monitor:
- Memory usage (most common cause)
- CPU utilization
- Disk I/O
- Network connectivity
- Number of concurrent players

**Q: How do I optimize performance?**
A:
- Regular restarts if memory leaks exist
- Optimize database queries
- Review and optimize scripts
- Monitor and limit resource-intensive operations

## Additional Resources

### Documentation Files
- `building.txt` - World building guide
- `coding.txt` - Programming reference
- `utils.txt` - Utility programs
- Platform-specific README files

### Online Resources
- Official forums and communities
- MUD development websites
- Technical documentation repositories
- Code sharing platforms

### Support Channels
- Community forums
- Developer mailing lists
- IRC channels
- Discord servers

---

## License and Credits

This documentation is based on the original CircleMUD and tbaMUD documentation, adapted for Luminari MUD. Please respect the original licenses and give credit where due.

**Original Credits:**
- DikuMUD team (original codebase)
- Jeremy Elson (CircleMUD)
- The Builder Academy (tbaMUD)
- Luminari MUD development team

**License:**
This software is provided under the CircleMUD license. See the `license.txt` file for complete terms and conditions.

---

*Last updated: July 2025*
*For the most current version of this documentation, check the official repository.*