# LuminariMUD Project, <PERSON><PERSON><PERSON> handled final acceptance of pull request

# Set to true to add reviewers to pull requests
addReviewers: true

# Set to true to add assignees to pull requests
addAssignees: true

# A list of reviewers to be added to pull requests (GitHub user name)
reviewers: 
  - GickerLDS
  - Mo<PERSON>hBenAvraham
  - <PERSON><PERSON><PERSON>

# A list of keywords to be skipped the process that add reviewers if pull requests include it 
skipKeywords:
  - wip

# A number of reviewers added to the pull request
# Set 0 to add all the reviewers (default: 0)
numberOfReviewers: 0
