# LuminariMUD .gitignore
# Based on C/C++ gitignore from https://github.com/github/gitignore/blob/master/
# Maintained by <PERSON>usuk

####################
# Build Artifacts
####################

# Prerequisites and dependencies
*.d
depend
*/depend

# Object files
*.o
*.ko
*.obj
*.elf
*.lo
*.slo

# Precompiled Headers
*.gch
*.pch

# Libraries
*.lib
*.a
*.la
*.lai

# Shared objects (inc. Windows DLLs)
*.dll
*.so
*.so.*
*.dylib

# Executables
*.exe
*.out
*.app
*.i*86
*.x86_64
*.hex

# Linker output
*.ilk
*.map
*.exp

# Debug files
*.dSYM/
*.su
*.idb
*.pdb

# Kernel Module Compile Results
*.mod*
*.cmd
.tmp_versions/
modules.order
Module.symvers
Mkfile.old
dkms.conf

# Fortran module files
*.mod
*.smod

####################
# MUD Executables
####################
# Ignore everything in bin directory
bin/*
# But keep the directory itself
!bin/.gitkeep

# Also ignore utility executables if they're built in util/
util/asciipasswd
util/autowiz
util/plrtoascii
util/rebuildAsciiIndex
util/rebuildMailIndex
util/shopconv
util/sign
util/split
util/webster
util/wld2html

####################
# IDE and Editor Files
####################
.vscode/
.idea/
.cursor/
.editorconfig
*.swp
*.swo
*~
*.code-workspace

####################
# AI Assistant Files
####################
# AI development guide and configuration
CLAUDE.md
.claude/
.cursorrules
.cursorignore

####################
# Backup Files
####################
*.bak
*.old
*.backup
bak/

####################
# Configure-Generated Files
####################
# These are generated by running ./configure
Makefile
util/Makefile
src/conf.h
config.status
config.cache
config.log

# Autoconf/automake generated files
autom4te.cache/
cnf/autom4te.cache/
cnf/configure
cnf/conf.h.in
configure
conf.h.in
.deps/
*.Po
*.Tpo
Makefile.in
aclocal.m4
missing
install-sh
depcomp
compile
.dirstamp
stamp-h1
# Temporary configure directories
confor*/

####################
# MUD Configuration Files
####################
# These contain sensitive or environment-specific settings
campaign.h
mysql_config
vnums.h
mud_options.h

####################
# Environment Files
####################
.env
.env.local
.env.production
.env.staging

####################
# Log Files
####################
*.log
log/*
!log/.gitkeep
syslog
syslog.*
syslog.CRASH

####################
# Debug Files
####################
gdb.txt
log/gdb.txt
.gdbcommands
.gdbinit_mud
core
core.*
vgcore.*

####################
# PHP Tools
####################
# Cache and temporary directories
cache/
logs/
tmp/
temp/
backups/

# Composer (if added later)
vendor/
composer.lock

####################
# MUD Runtime Data
####################

# Player files
lib/plrfiles/**/*.plr
lib/plrfiles/index
lib/plrobjs/**/*.objs

# House files
lib/house/*.house

# Runtime configuration and state files
lib/etc/badsites
lib/etc/board.*
lib/etc/clans
lib/etc/clan_investments
lib/etc/config
lib/etc/crafts
lib/etc/hcontrol
lib/etc/last
lib/etc/plrmail
lib/etc/time

# Autorun control files (should never be committed)
.killscript
.fastboot
pause
.websocket_policy.pid
.flash_policy.pid
.autorun.lock

# Copyover/hotboot files (should never be committed)
lib/copyover.dat

# User-generated content
lib/misc/bugs
lib/misc/ideas
lib/misc/messages
lib/misc/socials
lib/misc/socials.new
lib/misc/typos
lib/misc/xnames

# Mail system
lib/mudmail/*/
lib/mudmail/index

# Quest files
lib/world/hlq/*.hlq

####################
# World Files (OLC)
####################
# These are edited in-game via OLC and should not be in version control
lib/world/mob/*.mob
lib/world/obj/*.obj
lib/world/wld/*.wld
lib/world/zon/*.zon
lib/world/shp/*.shp
lib/world/trg/*.trg
lib/world/qst/*.qst

# World index files (generated)
lib/world/*/index
lib/world/*/index.mini
lib/world/*/index.backup

####################
# Text Files (In-Game Edited)
####################
# These are edited via tedit command in-game
lib/text/credits
lib/text/news
lib/text/motd
lib/text/imotd
lib/text/greetings
lib/text/info
lib/text/background
lib/text/handbook
lib/text/policies
lib/text/wizlist
lib/text/immlist
lib/text/oldnews

# Help files (legacy - now stored in MySQL via hedit)
lib/text/help/*
!lib/text/help/README
!lib/text/help/.gitkeep

####################
# Unit Tests
####################
/unittests/obj
/unittests/CuTest/AllTests.c

####################
# Keep Directory Structure
####################
# Preserve empty directories with .gitkeep files
!lib/plrfiles/.gitkeep
!lib/plrfiles/*/.gitkeep
!lib/plrfiles/*/00
!lib/plrobjs/.gitkeep
!lib/plrobjs/*/.gitkeep
!lib/plrobjs/*/00
!lib/etc/.gitkeep
!lib/house/.gitkeep
!lib/misc/.gitkeep
!lib/mudmail/.gitkeep
!lib/world/hlq/.gitkeep
!lib/world/mob/.gitkeep
!lib/world/obj/.gitkeep
!lib/world/wld/.gitkeep
!lib/world/zon/.gitkeep
!lib/world/shp/.gitkeep
!lib/world/trg/.gitkeep
!lib/world/qst/.gitkeep
circle

# Ignore tar and gz files in root
/*.tar
/*.gz
/*.tar.gz
