Updated: Apr 2007
                           Compiling tbaMUD
                        under Microsoft Windows

There are a number of different compilers available for Microsoft Windows that 
can be used to compile tbaMUD.  Each compiler works differently, so each 
compiler has a different set of instructions.

We currently have instructions for using the following compilers:

1.  Cygnus CYGWIN (Formerly Cygnus GNU-Win32).  This is a FREE compiler for 
Microsoft Windows!  If you don't own a compiler, you can just download this 
one off the net and get tbaMUD up and running in no time.  Read the file 
README.CYGWIN.

2.  Microsoft Visual C++ version 4.x -- read the file README.MSVC4.

3.  Microsoft Visual C++ version 5.x -- read the file README.MSVC5.

4.  Microsoft Visual C++ version 6.x -- read the file README.MSVC6.

5.  Microsoft Visual C++ version 8.x -- read the file README.MSVC8. This is 
    also called Microsoft Visual C++ 2005 and the express edition is free and 
    can be downloaded from Microsoft.

6.  Borland C++ -- read the file README.BORLAND

7.  Watcom C++ v.11 -- read the file README.WATCOM

It should be possible to compile tbaMUD using other compilers that are not 
listed here, or other versions of these compilers -- as long as they support 
long filenames, etc.  However, we don't have instructions handy for those 
compilers. Feel free to submit your own.

Have fun!
For help, check out http://www.cwg.lazuras.org

Originally written by: <PERSON> <PERSON>son 
