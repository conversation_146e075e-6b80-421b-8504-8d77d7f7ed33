[Unit]
Description=LuminariMUD Game Server
After=network.target mysql.service

[Service]
Type=forking
User=luminari
Group=luminari
WorkingDirectory=/home/<USER>/luminarimud
# autorun.sh now defaults to daemon mode, no arguments needed
ExecStart=/home/<USER>/luminarimud/autorun.sh
# Use the built-in stop command instead of .killscript
ExecStop=/home/<USER>/luminarimud/autorun.sh stop
Restart=always
RestartSec=60
# Environment variables
Environment="MUD_PORT=4100"
Environment="MUD_FLAGS=-q"

# Security settings
NoNewPrivileges=true
PrivateTmp=true

# Resource limits
LimitNOFILE=4096
LimitCORE=infinity

[Install]
WantedBy=multi-user.target