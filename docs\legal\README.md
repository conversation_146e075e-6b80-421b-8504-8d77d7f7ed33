# Legal Documentation

This document provides comprehensive licensing details for Luminari MUD.

**Quick Reference**: See [`LICENSE.md`](../../LICENSE.md) in the root directory for the summary.

## Table of Contents

1. [Overview](#overview)
2. [Luminari MUD License](#luminari-mud-license)
3. [tbaMUD License](#tbamud-license)
4. [CircleMUD License](#circlemud-license)
5. [DikuMUD License](#dikumud-license)
6. [Compliance Requirements](#compliance-requirements)
7. [Attribution Requirements](#attribution-requirements)
8. [Distribution Guidelines](#distribution-guidelines)

## Overview

Luminari MUD is built upon a foundation of several MUD codebases, each with their own licensing requirements. Understanding and complying with these licenses is essential for anyone using, modifying, or distributing Luminari MUD code.

### License Hierarchy

```
DikuMUD (1990-1991)
    ↓
CircleMUD (1994-2001)
    ↓
tbaMUD (2001-present) + CWG + d20MUD [CWG & d20MUD share same license as tbaMUD]
    ↓
Luminari MUD (2010-present)
```

Each layer adds its own licensing requirements while maintaining compliance with all previous licenses.

## Luminari MUD License

**Custom Luminari MUD code is released into the public domain under The Unlicense.**

Any custom code made on LuminariMUD is free and unencumbered software released into the public domain:

Anyone is free to copy, modify, publish, use, compile, sell, or distribute this software, either in source code form or as a compiled binary, for any purpose, commercial or non-commercial, and by any means.

In jurisdictions that recognize copyright laws, the author or authors of this software dedicate any and all copyright interest in the software to the public domain. We make this dedication for the benefit of the public at large and to the detriment of our heirs and successors. We intend this dedication to be an overt act of relinquishment in perpetuity of all present and future rights to this software under copyright law.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

For more information, please refer to <https://unlicense.org>

**Important Note:** This public domain dedication applies ONLY to custom Luminari MUD code. All inherited code from tbaMUD, CircleMUD, and DikuMUD remains subject to their respective licenses.

## tbaMUD License

Code contributed by the tbaMUD project follows under whatever license they utilize. For current tbaMUD licensing information, visit [tbamud.com](http://tbamud.com).

tbaMUD is a continuation and enhancement of CircleMUD, maintaining compatibility with CircleMUD licensing while adding modern features and improvements.

## CircleMUD License

**Copyright (C) 1995 by Jeremy Elson - All Rights Reserved**

CircleMUD was created by:
- Jeremy Elson
- Department of Computer Science
- Johns Hopkins University
- Baltimore, MD 21218 USA
- <EMAIL>

### CircleMUD License Terms

CircleMUD is licensed software. Use of the CircleMUD system in any way, or use of any of its source code, requires compliance with this license.

CircleMUD is a derivative work based on the DikuMUD system. You are legally bound to comply with both the CircleMUD license and the original DikuMUD license.

#### Key Requirements

The CircleMUD license requires three main things:

1. **No Commercial Use**: You must not use CircleMUD to make money or be compensated in any way
2. **Attribution**: You must give the authors credit for their work
3. **DikuMUD Compliance**: You must comply with the DikuMUD license

#### Detailed Commercial Use Restrictions

- No fees or donations for playing the game
- No soliciting money for running the MUD
- No selling CircleMUD or charging for distribution
- No reimbursement for expenses related to running CircleMUD

#### Attribution Requirements

- Preserve the 'credits' file text completely
- Maintain the "CIRCLEMUD" help entry intact
- Include DikuMUD and CircleMUD creator names in login sequence
- Distribute this license with all copies
- Do not remove copyright, licensing, or authorship notices
- Claims of "total rewrite" do not exempt from license requirements

## DikuMUD License

**Copyright (C) 1990, 1991 - All Rights Reserved**

### DikuMUD Creators

- **Sebastian Hammer** - <EMAIL>
- **Michael Seifert** - <EMAIL>
- **Hans Henrik Stærfeldt** - <EMAIL>
- **Tom Madsen** - <EMAIL>
- **Katja Nyboe** - <EMAIL>

Created at DIKU (Computer Science Institute at Copenhagen University)

### DikuMUD License Rules

**DikuMUD is NOT Public Domain, shareware, careware or the like**

#### Restrictions

1. **No Profit**: You may under no circumstances make profit on ANY part of DikuMUD in any possible way
2. **No Distribution Fees**: You may not charge money for distributing any part of DikuMUD
3. **Copyright Preservation**: You may not remove any copyright notices
4. **License Inclusion**: This license must always be included "as is" with any distribution

#### Requirements

1. **Publication Notice**: If you publish any part of DikuMUD, creators must appear in the article with proper copyright
2. **Setup Notification**: You must notify the creators when setting up a DikuMUD version
3. **Login Credits**: Running versions must include creator names in login sequence
4. **Credits Command**: The "credits" command must contain creator names and addresses

## Compliance Requirements

### For Luminari MUD Users

When using Luminari MUD, you must comply with ALL applicable licenses:

1. **DikuMUD License** - For all inherited DikuMUD code
2. **CircleMUD License** - For all inherited CircleMUD code
3. **tbaMUD License** - For all inherited tbaMUD code
4. **Luminari License** - Custom Luminari code is public domain

### Key Compliance Points

- **No Commercial Use** of any inherited code (DikuMUD, CircleMUD, tbaMUD portions)
- **Proper Attribution** in credits, help files, and login sequences
- **License Distribution** with any copies or derivatives
- **Notification** to original creators when required
- **Copyright Preservation** in all source files

## Attribution Requirements

### Required Credits

Your MUD must include proper attribution for:

1. **DikuMUD Creators** - In login sequence and credits command
2. **CircleMUD Creator** - Jeremy Elson in login sequence and help files
3. **tbaMUD Contributors** - As specified by tbaMUD project
4. **Luminari Contributors** - Optional (public domain code)

### Login Sequence

The login sequence must contain the names of:
- DikuMUD creators
- CircleMUD creator (Jeremy Elson)
- tbaMUD contributors (as applicable)

### Help Files

- Maintain "CIRCLEMUD" help entry intact
- Include DikuMUD creator information
- Preserve all existing attribution text

### Credits Command

Must display:
- DikuMUD creator names and addresses
- CircleMUD creator information
- tbaMUD contributor information
- Notice that DikuMUD creators created the original system

## Distribution Guidelines

### What You Can Do

- Use and modify the code for non-commercial purposes
- Distribute modified versions (with proper licensing)
- Create derivative works (following all license terms)
- Run public MUDs (non-commercial)

### What You Cannot Do

- Sell the software or charge for distribution
- Use for commercial purposes or profit
- Remove copyright or licensing notices
- Claim authorship of inherited code
- Distribute without required licenses

### Distribution Checklist

When distributing Luminari MUD or derivatives:

- [ ] Include all required license files
- [ ] Preserve all copyright notices in source code
- [ ] Maintain proper attribution in credits and help files
- [ ] Ensure login sequence includes required creator names
- [ ] Verify no commercial use restrictions are violated
- [ ] Include this legal documentation

## Contact Information

### For Licensing Questions

- **DikuMUD**: Contact original creators (see addresses above)
- **CircleMUD**: Jeremy Elson - <EMAIL>
- **tbaMUD**: Visit [tbamud.com](http://tbamud.com) <NAME_EMAIL>
- **Luminari MUD**: Check project repository for current maintainer information

### Legal Compliance

This document is provided for informational purposes. For legal advice regarding license compliance, consult with a qualified attorney familiar with software licensing.

---

**Last Updated**: 2025-07-27
**Document Version**: 1.0
**Consolidated From**: LICENSE, docs/old_doc/license.txt, docs/old_doc/license.doc