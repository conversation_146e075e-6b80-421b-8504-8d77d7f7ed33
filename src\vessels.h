/* ************************************************************************
 *      File:   vessels.h                            Part of LuminariMUD  *
 *   Purpose:   Unified Vessel/Vehicle system header                      *
 *  Systems:    CWG Vehicles, Outcast Ships, Greyhawk Ships               *
 * ************************************************************************ */

#ifndef _VESSELS_H_
#define _VESSELS_H_

/*
 * Feature toggles
 * Enable subsystems incrementally to reduce initial compile/link risk.
 * Default: CWG enabled, Outcast and Greyhawk disabled.
 */
#ifndef VESSELS_ENABLE_CWG
#define VESSELS_ENABLE_CWG 1
#endif

#ifndef VESSELS_ENABLE_OUTCAST
#define VESSELS_ENABLE_OUTCAST 0
#endif

#ifndef VESSELS_ENABLE_GREYHAWK
#define VESSELS_ENABLE_GREYHAWK 0
#endif

#include "conf.h"
#include "sysdep.h"
#include "structs.h"
#include "utils.h"
#include "comm.h"
#include "db.h"
#include "oasis.h"
#include "screen.h"
#include "interpreter.h"
#include "modify.h"
#include "handler.h"
#include "constants.h"

/* ========================================================================= */
/* ITEM TYPES FOR VESSEL SYSTEM                                              */
/* ========================================================================= */

#define ITEM_VEHICLE                53  /* Vehicle object - represents the actual vehicle */
#define ITEM_CONTROL                54  /* Control mechanism - steering wheel, helm, etc. */
#define ITEM_HATCH                  55  /* Exit/entry point - doorway out of vehicle */

/* ========================================================================= */
/* ROOM FLAGS FOR VESSEL SYSTEM                                              */
/* ========================================================================= */

#define ROOM_VEHICLE                40  /* Room that vehicles can move through */

/* ========================================================================= */
/* FUTURE ADVANCED VESSEL SYSTEM CONSTANTS                                   */
/* ========================================================================= */
/* These constants are for the advanced vessel system, not the CWG system    */

/* Vessel Types */
#define VESSEL_TYPE_SAILING_SHIP    1   /* Ocean-going ships */
#define VESSEL_TYPE_SUBMARINE       2   /* Underwater vessels */
#define VESSEL_TYPE_AIRSHIP         3   /* Flying craft */
#define VESSEL_TYPE_STARSHIP        4   /* Space vessels */
#define VESSEL_TYPE_MAGICAL_CRAFT   5   /* Magically powered vehicles */

/* Vessel States */
#define VESSEL_STATE_DOCKED         0   /* Parked/anchored */
#define VESSEL_STATE_TRAVELING      1   /* Moving between locations */
#define VESSEL_STATE_COMBAT         2   /* In battle */
#define VESSEL_STATE_DAMAGED        3   /* Broken down */

/* Vessel Sizes */
#define VESSEL_SIZE_SMALL           1   /* 1-2 passengers */
#define VESSEL_SIZE_MEDIUM          2   /* 3-6 passengers */
#define VESSEL_SIZE_LARGE           3   /* 7-15 passengers */
#define VESSEL_SIZE_HUGE            4   /* 16+ passengers */

/* ========================================================================= */
/* DIRECTION CONSTANTS                                                       */
/* ========================================================================= */

#ifndef OUTDIR
#define OUTDIR                      6   /* Generic "out" direction for vehicles */
#endif

/* ========================================================================= */
/* UNIFIED FACADE API                                                        */
/* ========================================================================= */

enum vessel_command {
  VESSEL_CMD_NONE = 0,
  VESSEL_CMD_DRIVE,          /* CWG drive */
  VESSEL_CMD_SAIL_MOVE,      /* Outcast move */
  VESSEL_CMD_SAIL_SPEED,     /* Outcast speed */
  VESSEL_CMD_GH_TACTICAL,    /* Greyhawk tactical */
  VESSEL_CMD_GH_STATUS,      /* Greyhawk status */
};

struct vessel_result {
  int success;               /* boolean */
  int error_code;            /* 0 success */
  char message[256];
  void *result_data;
};

/* Initialization entry point to be called at boot */
void vessel_init_all(void);

/* Unified command executor (optional facade) */
struct vessel_result vessel_execute_command(struct char_data *actor,
                                            enum vessel_command cmd,
                                            const char *argument);

/* ========================================================================= */
/* FUNCTION PROTOTYPES - FUTURE ADVANCED VESSEL SYSTEM                       */
/* ========================================================================= */
/* These functions are placeholders for a future advanced vessel system      */

void load_vessels(void);                                /* Load vessel data from storage */
void save_vessels(void);                                /* Save vessel data to storage */
struct vessel_data *find_vessel_by_id(int vessel_id);   /* Find vessel by unique ID */

void vessel_movement_tick(void);                        /* Process vessel movement each tick */
void enter_vessel(struct char_data *ch, struct vessel_data *vessel);  /* Board a vessel */
void exit_vessel(struct char_data *ch);                 /* Leave a vessel */
int can_pilot_vessel(struct char_data *ch, struct vessel_data *vessel); /* Check piloting ability */
void pilot_vessel(struct char_data *ch, int direction); /* Pilot vessel in direction */

/* ========================================================================= */
/* FUNCTION PROTOTYPES - CWG VEHICLE SYSTEM (READY TO USE)                   */
/* ========================================================================= */
#if VESSELS_ENABLE_CWG

/* Object Finding Functions */
struct obj_data *find_vehicle_by_vnum(int vnum);       /* Find vehicle object by vnum */
struct obj_data *get_obj_in_list_type(int type, struct obj_data *list); /* Find object of type in list */
struct obj_data *find_control(struct char_data *ch);   /* Find vehicle controls near player */

/* Vehicle Movement Functions */
void drive_into_vehicle(struct char_data *ch, struct obj_data *vehicle, char *arg); /* Drive into another vehicle */
void drive_outof_vehicle(struct char_data *ch, struct obj_data *vehicle);          /* Drive out of vehicle */
void drive_in_direction(struct char_data *ch, struct obj_data *vehicle, int dir);  /* Drive in a direction */

/* CWG System Commands (Ready to Use) */
ACMD(do_drive);         /* Drive a vehicle - main command for CWG system */

#endif /* VESSELS_ENABLE_CWG */

/* ========================================================================= */
/* OUTCAST SHIP SYSTEM CONSTANTS AND STRUCTURES                              */
/* ========================================================================= */
#if VESSELS_ENABLE_OUTCAST

#ifndef MAX_NUM_SHIPS
#define MAX_NUM_SHIPS               50  /* Maximum number of ships in game */
#endif

#ifndef MAX_NUM_ROOMS
#define MAX_NUM_ROOMS               20  /* Maximum rooms per ship */
#endif

#define SHIP_MAX_SPEED              30  /* Maximum ship speed value */
#define ITEM_SHIP                   56  /* Ship object type (avoid conflicts) */
#define DOCKABLE                    41  /* Room flag for dockable areas */

/* Outcast Ship Data Structure */
struct outcast_ship_data {
  int hull;                             /* max hull points */
  int speed;                            /* max speed */
  int capacity;                         /* max number of characters in ship */
  int damage;                           /* amount of damage (for firing) */

  int size;                             /* size of the vehicle (for ramming) */
  int velocity;                         /* current velocity */

  struct obj_data *obj;                 /* vehicle object */
  int obj_num;                          /* vehicle object number */

  int timer;                            /* timer for ship action other than moving */
  int move_timer;                       /* timer for ship movement */
  int lastdir;                          /* last direction for the ship */
  int repeat;                           /* autopilot */

  int in_room;                          /* room containing this ship */
  int entrance_room;                    /* room to enter/exit ship */
  int num_room;                         /* number of rooms in this vehicle */
  int room_list[MAX_NUM_ROOMS + 1];     /* room numbers in this vehicle */

  int dock_vehicle;                     /* docked to another ship: -1 is not docked */
};

/* Navigation Data Structure */
struct outcast_navigation_data {
  int mob;                              /* mob id that can control ship */
  bool sail;
  int control_room;                     /* control room for this mob to become a navigator */
  char *path1, *path2;                  /* path for going and returning */
  char *path;
  int start1;                           /* start room */
  int destination1;                     /* destination */
  int destination;                      /* initially set to zero */
  int sail_time;                        /* time ship start sailing */
  int freq;                             /* ship sail once every 'freq' hours */
};

/* FUNCTION PROTOTYPES - OUTCAST SHIP SYSTEM */
void initialize_outcast_ships(void);
void outcast_ship_activity(void);
int find_outcast_ship(struct obj_data *obj);
bool is_outcast_ship_docked(int t_ship);
bool is_valid_outcast_ship(int t_ship);
int in_which_outcast_ship(struct char_data *ch);
void sink_outcast_ship(int t_ship);
bool move_outcast_ship(int t_ship, int dir, struct char_data *ch);
int outcast_navigation(struct char_data *ch, int mob, int t_ship);
int outcast_ship_proc(struct obj_data *obj, struct char_data *ch, int cmd, char *arg);
int outcast_control_panel(struct obj_data *obj, struct char_data *ch, int cmd, char *argument);
int outcast_ship_exit_room(int room, struct char_data *ch, int cmd, char *arg);
int outcast_ship_look_out_room(int room, struct char_data *ch, int cmd, char *arg);

#endif /* VESSELS_ENABLE_OUTCAST */

/* ========================================================================= */
/* GREYHAWK SHIP SYSTEM CONSTANTS AND STRUCTURES                             */
/* ========================================================================= */
#if VESSELS_ENABLE_GREYHAWK

#ifndef GREYHAWK_MAXSHIPS
#define GREYHAWK_MAXSHIPS           500  /* Maximum number of ships in game */
#endif

#ifndef GREYHAWK_MAXSLOTS
#define GREYHAWK_MAXSLOTS           10   /* Maximum equipment slots per ship */
#endif

/* Ship Position Constants */
#define GREYHAWK_FORE               0    /* Forward position */
#define GREYHAWK_PORT               1    /* Port (left) position */
#define GREYHAWK_REAR               2    /* Rear position */
#define GREYHAWK_STARBOARD          3    /* Starboard (right) position */

/* Weapon Range Types */
#define GREYHAWK_SHRTRANGE          0    /* Short range */
#define GREYHAWK_MEDRANGE           1    /* Medium range */
#define GREYHAWK_LNGRANGE           2    /* Long range */

/* Item Type for Greyhawk Ships */
#define GREYHAWK_ITEM_SHIP          57   /* Greyhawk ship object type (avoid conflicts) */

/* Greyhawk Ship Equipment Slot Structure */
struct greyhawk_ship_slot {
  char type;                            /* Type of slot (1=weapon, 2=oarsman, 3=ammo) */
  char position;                        /* Position: FORE/PORT/REAR/STARBOARD */
  unsigned char weight;                 /* Weight of equipment */
  char desc[256];                       /* Description of slot equipment */
  char val0, val1, val2, val3;          /* Equipment values (range, damage, etc.) */
  unsigned char x, y;                   /* Slot x,y position on ship room */
  short int timer;                      /* Reload/action timer */
};

/* Greyhawk Ship Crew Structure */
struct greyhawk_ship_crew {
  char crewname[256];                   /* Crew description */
  char speedadjust;                     /* Speed adjustment modifier */
  char gunadjust;                       /* Gunnery adjustment modifier */
  char repairspeed;                     /* Repair speed modifier */
};

/* Greyhawk Ship Data Structure */
struct greyhawk_ship_data {
  /* Armor System - different sides of ship */
  unsigned char maxfarmor, maxrarmor, maxparmor, maxsarmor;    /* Max armor values */
  unsigned char maxfinternal, maxrinternal, maxsinternal, maxpinternal; /* Max internal */
  unsigned char farmor, finternal;      /* Fore armor/internal current */
  unsigned char rarmor, rinternal;      /* Rear armor/internal current */
  unsigned char sarmor, sinternal;      /* Starboard armor/internal current */
  unsigned char parmor, pinternal;      /* Port armor/internal current */

  /* Ship Performance */
  unsigned char maxturnrate, turnrate;  /* Maximum/current turn rate */
  unsigned char mainsail, maxmainsail;  /* Main sail HP/condition */
  unsigned char hullweight;             /* Weight of hull (in thousands) */
  unsigned char maxslots;               /* Maximum number of equipment slots */

  /* Position and Movement */
  float x, y, z;                        /* Current coordinates */
  float dx, dy, dz;                     /* Delta movement vectors */

  /* Crew */
  struct greyhawk_ship_crew sailcrew;   /* Sailing crew */
  struct greyhawk_ship_crew guncrew;    /* Gunnery crew */

  /* Equipment */
  struct greyhawk_ship_slot slot[GREYHAWK_MAXSLOTS]; /* Equipment slots */

  /* Identification */
  char owner[64];                       /* Ship owner name */
  struct obj_data *shipobj;             /* Associated ship object */
  char name[128];                       /* Ship name */
  char id[3];                           /* Ship ID designation (AA-ZZ) */

  /* Location and Status */
  int dock;                             /* Docked room number */
  int shiproom;                         /* Ship interior room vnum */
  int shipnum;                          /* Ship index number */
  int location;                         /* Current world location */

  /* Navigation */
  short int heading;                    /* Current heading (0-360) */
  short int setheading;                 /* Set heading (target) */
  short int minspeed, maxspeed;         /* Speed range */
  short int speed, setspeed;            /* Current and target speed */

  /* Events */
  struct event *action;                 /* Ship action event */
};

/* Greyhawk Contact Data Structure */
struct greyhawk_contact_data {
  int shipnum;                          /* Ship number being tracked */
  int x, y, z;                          /* Contact coordinates */
  int bearing;                          /* Bearing to contact */
  float range;                          /* Range to contact */
  char arc[3];                          /* Firing arc (F/P/R/S) */
};

/* Greyhawk Ship Action Event Structure */
struct greyhawk_ship_action_event {
  int shipnum;                          /* Ship performing action */
};

/* Greyhawk Tactical Map Structure */
struct greyhawk_ship_map {
  char map[10];                         /* Map symbol representation */
};

/* GREYHAWK SHIP SYSTEM MACROS (subset used by implementation) */
#define GREYHAWK_SHIPMAXFARMOR(in_room)    world[(in_room)].ship->maxfarmor
#define GREYHAWK_SHIPMAXRARMOR(in_room)    world[(in_room)].ship->maxrarmor
#define GREYHAWK_SHIPMAXPARMOR(in_room)    world[(in_room)].ship->maxparmor
#define GREYHAWK_SHIPMAXSARMOR(in_room)    world[(in_room)].ship->maxsarmor
#define GREYHAWK_SHIPFARMOR(in_room)       world[(in_room)].ship->farmor
#define GREYHAWK_SHIPRARMOR(in_room)       world[(in_room)].ship->rarmor
#define GREYHAWK_SHIPPARMOR(in_room)       world[(in_room)].ship->parmor
#define GREYHAWK_SHIPSARMOR(in_room)       world[(in_room)].ship->sarmor
#define GREYHAWK_SHIPMAINSAIL(in_room)     world[(in_room)].ship->mainsail
#define GREYHAWK_SHIPMAXMAINSAIL(in_room)  world[(in_room)].ship->maxmainsail

#define GREYHAWK_SHIPMAXRINTERNAL(in_room) world[(in_room)].ship->maxrinternal
#define GREYHAWK_SHIPMAXFINTERNAL(in_room) world[(in_room)].ship->maxfinternal
#define GREYHAWK_SHIPMAXPINTERNAL(in_room) world[(in_room)].ship->maxpinternal
#define GREYHAWK_SHIPMAXSINTERNAL(in_room) world[(in_room)].ship->maxsinternal
#define GREYHAWK_SHIPFINTERNAL(in_room)    world[(in_room)].ship->finternal
#define GREYHAWK_SHIPRINTERNAL(in_room)    world[(in_room)].ship->rinternal
#define GREYHAWK_SHIPPINTERNAL(in_room)    world[(in_room)].ship->pinternal
#define GREYHAWK_SHIPSINTERNAL(in_room)    world[(in_room)].ship->sinternal
#define GREYHAWK_SHIPHULLWEIGHT(in_room)   world[(in_room)].ship->hullweight
#define GREYHAWK_SHIPMAXSLOTS(in_room)     world[(in_room)].ship->maxslots

#define GREYHAWK_SHIPSAILNAME(in_room)     world[(in_room)].ship->sailcrew.crewname
#define GREYHAWK_SHIPGUNNAME(in_room)      world[(in_room)].ship->guncrew.crewname
#define GREYHAWK_SHIPSLOT(in_room)         world[(in_room)].ship->slot

#define GREYHAWK_SHIPID(in_room)           world[(in_room)].ship->id
#define GREYHAWK_SHIPOWNER(in_room)        world[(in_room)].ship->owner
#define GREYHAWK_SHIPNAME(in_room)         world[(in_room)].ship->name
#define GREYHAWK_SHIPNUM(in_room)          world[(in_room)].ship->shipnum
#define GREYHAWK_SHIPOBJ(in_room)          world[(in_room)].ship->shipobj

#define GREYHAWK_SHIPX(in_room)            world[(in_room)].ship->x
#define GREYHAWK_SHIPY(in_room)            world[(in_room)].ship->y
#define GREYHAWK_SHIPZ(in_room)            world[(in_room)].ship->z
#define GREYHAWK_SHIPHEADING(in_room)      world[(in_room)].ship->heading
#define GREYHAWK_SHIPSETHEADING(in_room)   world[(in_room)].ship->setheading
#define GREYHAWK_SHIPSPEED(in_room)        world[(in_room)].ship->speed
#define GREYHAWK_SHIPSETSPEED(in_room)     world[(in_room)].ship->setspeed
#define GREYHAWK_SHIPMAXSPEED(in_room)     world[(in_room)].ship->maxspeed
#define GREYHAWK_SHIPLOCATION(in_room)     world[(in_room)].ship->location
#define GREYHAWK_SHIPMINSPEED(in_room)     world[(in_room)].ship->minspeed

/* FUNCTION PROTOTYPES - GREYHAWK SHIP SYSTEM */
void greyhawk_initialize_ships(void);
int greyhawk_loadship(int template, int to_room, short int x_cord, short int y_cord, short int z_cord);
void greyhawk_nameship(char *name, int shipnum);
bool greyhawk_setsail(int class, int shipnum);

void greyhawk_getstatus(int slot, int rnum);
void greyhawk_getposition(int slot, int rnum);
void greyhawk_dispweapon(int slot, int rnum);

int greyhawk_bearing(float x1, float y1, float x2, float y2);
float greyhawk_range(float x1, float y1, float z1, float x2, float y2, float z2);
int greyhawk_weaprange(int shipnum, int slot, char range);

void greyhawk_dispcontact(int i);
int greyhawk_getcontacts(int shipnum);
void greyhawk_setcontact(int i, struct obj_data *obj, int shipnum, int xoffset, int yoffset);
int greyhawk_getarc(int ship1, int ship2);

void greyhawk_getmap(int shipnum);
void greyhawk_setsymbol(int x, int y, int symbol);

/* Greyhawk specials exposed as plain functions for Luminari SPECIAL binding */
int greyhawk_ship_commands(struct obj_data *obj, struct char_data *ch, int cmd, char *argument);
int greyhawk_ship_object(struct obj_data *obj, struct char_data *ch, int cmd, char *argument);
int greyhawk_ship_loader(struct obj_data *obj, struct char_data *ch, int cmd, char *argument);

#endif /* VESSELS_ENABLE_GREYHAWK */

/* ========================================================================= */
/* COMMAND PROTOTYPES (ADVANCED PLACEHOLDERS)                                */
/* ========================================================================= */
ACMD(do_board);         /* Board a vessel */
ACMD(do_disembark);     /* Leave a vessel */
ACMD(do_pilot);         /* Pilot a vessel */
ACMD(do_vessel_status); /* Show vessel status */

#endif /* _VESSELS_H_ */