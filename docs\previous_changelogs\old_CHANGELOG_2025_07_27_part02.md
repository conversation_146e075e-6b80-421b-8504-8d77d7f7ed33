# CHANGELOG

## 2025-07-27

### Major Feature: OpenAI Integration

**Implemented complete OpenAI integration for dynamic NPC dialogue**

#### Core Implementation
- Added `ai_service.c/h` - Main AI service module with CURL integration
- Added `ai_cache.c` - In-memory response caching with <PERSON>R<PERSON> eviction
- Added `ai_security.c` - API key encryption and input sanitization
- Added `ai_events.c` - Event system integration for delayed responses
- Added `dotenv.c/h` - Environment variable parser for configuration
- Modified `act.comm.c` - Enhanced do_tell to support AI-powered NPCs
- Modified `act.wizard.c` - Added 'ai' admin command for service management
- Modified `interpreter.c` - Registered new 'ai' command
- Modified `structs.h` - Added MOB_AI_ENABLED flag (bit 98)
- Modified `constants.c` - Added "AI-Enabled" to action_bits array
- Modified `Makefile.in` - Added AI service compilation rules

#### Configuration
- Created `.env.example` - Configuration template with detailed documentation
- Environment variables loaded from `lib/.env` file
- Supports multiple OpenAI models (gpt-4.1-mini default)
- Configurable rate limiting and caching

#### Database Changes
- Added `ai_config` table for configuration storage
- Added `ai_requests` table for usage logging
- Added `ai_cache` table for persistent caching option
- Added `ai_npc_personalities` table for NPC customization
- Created stored procedures for cache cleanup and usage statistics

#### Security Features
- API keys stored encrypted (XOR cipher, AES recommended for production)
- Input sanitization for all prompts
- Secure memory handling for sensitive data
- Rate limiting to prevent API abuse

#### Performance Optimizations
- Response caching with 1-hour default TTL
- LRU cache eviction when size exceeds limit
- Event-based delayed responses for natural conversation flow
- Retry logic with exponential backoff

#### Documentation
- Created `AI_SERVICE_README.md` - Complete setup and usage guide
- Updated `OPEN_AI.md` - Marked as IMPLEMENTED with usage instructions
- Updated `OPEN_AI_TECH.md` - Added implementation notes and actual code
- Created `ai_service_migration.sql` - Database migration script

#### Known Limitations
- XOR encryption is basic (upgrade to AES for production)
- Simplified JSON parsing (full json-c integration optional)
- medit support for AI flag not yet implemented

#### Usage
```
# Enable AI service
ai enable

# Test connectivity
ai test

# Enable AI for an NPC (set MOB_AI_ENABLED flag)
# Then: tell <npc_name> Hello!
```

#### Cost Considerations
- Default model gpt-4.1-mini is 83% cheaper than gpt-4o
- Alternative gpt-4o-mini at $0.15/1M input tokens
- Aggressive caching reduces API calls by ~70%
